<div class="modal fade" id="searchCustomerBookingModal" tabindex="-1" role="dialog" aria-labelledby="demoModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="searchCustomerBookingModalTitle">Search Your Order</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="search_customer_order_form">
                    @csrf
                    <div class="form-group row mx-0 col-md-12">
                        <label for="search_customer_order_order_code" class="col-md-4 col-form-label">Order Code</label>
                        <input type="text" id="search_customer_order_order_code"
                            name="search_customer_order_order_code" class="col-md-8">
                    </div>
                </form>
                <table id="result_table"></table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="submitbutton" style="background-color:#9E2A9B"
                    onclick="submit_to_search_customer_order()">Submit</button>
            </div>
        </div>
    </div>
</div>
@push('js')
    <script>
        function submit_to_search_customer_order() {
            $("#result_table tr").remove();
            var search_customer_order_form = document.getElementById('search_customer_order_form');
            var result_table = document.getElementById('result_table');
            $.ajax({
                type: "POST",
                url: "{{ route('search_customer_order') }}",
                dataType: "json",
                data: $('#search_customer_order_form').serialize(),
                success: function(data) {
                    if (data.status) {
                        window.open("{{ route('view_order_link') }}?code=" + data.order_code, "_blank");
                        // var result_array = data.order_array;
                        // var key = Object.keys(result_array);
                        // var value = Object.values(result_array);
                        // for (var i = key.length; i >= 0; i--) {
                        //     var row = result_table.insertRow(0);
                        //     var cell_one = row.insertCell(0);
                        //     cell_one.innerHTML = key[i];
                        //     var cell_two = row.insertCell(1);
                        //     cell_two.innerHTML = value[i];
                        // }
                    } else {
                        var no_result_row = result_table.insertRow(0);
                        var no_result_cell = no_result_row.insertCell(0);
                        no_result_cell.innerHTML = 'No Result';
                    }
                }
            });
        }
    </script>
