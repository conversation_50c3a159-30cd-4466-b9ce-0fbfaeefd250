@extends('landing.layouts.main')
@section('title', 'Full Time Private Driver Malaysia | Private Chauffeur Hire KL')
@section('description', 'Looking to be a Full Time Private Driver in Malaysia? GoMyHire is expanding nationwide! Apply for Private Chauffeur Hire KL positions and start hustling today.')
@push('css')
    <link rel="stylesheet" href="{{ asset('/css/partials/driver.css') }}">
@endpush
@section('content')
<div class="content" style="background-color: #F8F7F6">
    <section class="driver_banner">
        <div class="banner_bg">
            <div class="banner_content">
                <h1 style="color: #FFFFFF;">
                    Full Time Private Driver Malaysia | Private Chauffeur Hire KL
                </h1>
                <p style="color: #FFFFFF;">
                    Looking to be a Full Time Private Driver in Malaysia? GoMyHire is expanding
                    nationwide! Apply for Private Chauffeur Hire KL positions and start hustling today.
                </p>
                <div class="app-buttons">
                    <a href="https://play.google.com/store/apps" class="store-icon">
                        <img src="{{ asset('images/landing/google-play.png') }}" alt="Google Play">
                    </a>
                    <a href="https://www.apple.com/app-store/" class="store-icon">
                        <img src="{{ asset('images/landing/app-store.png') }}" alt="App Store">
                    </a>
                    <a href="" style="color:#FFFFFF;">Download APK for Web</a>
                </div>
            </div>
        </div>
    </section>
    @include('landing.partials.driver.features')
    @include('landing.partials.driver.how_to')
    @include('landing.partials.driver.plan')
    @include('landing.partials.driver.feedback')
    @include('landing.partials.driver.questions')
    @include('landing.partials.driver.register')
    @include('landing.footer')
</div>
@endsection

@push('js')
<script>
    $(document).ready(function() {
        $('.toggle-btn').on('click', function() {
            $(this).siblings('.answer').slideToggle();
        });
    });
</script>
@endpush
