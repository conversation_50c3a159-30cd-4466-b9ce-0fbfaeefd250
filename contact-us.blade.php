@extends('landing.layouts.main')
@section('title', 'Book Chauffeur Service in KL | Best Private Transport Malaysia')
@section('description', 'Need private chauffeur services? GoMyHire provides the Best Private Transport in Malaysia. Book Chauffeur Services in KL for a smooth and comfy ride now!')
@push('css')
    <link rel="stylesheet" href="{{ asset('/css/partials/contact.css') }}">
@endpush
@section('content')
    <div class="content" style="background-color: #F8F7F6">
        <div class="contact-relative">
            <section class="contact-section">
                <div class="contact-background">
                    <div class="contact_content">
                        <h1 style="color: #FFFFFF;">Book Chauffeur Service in KL | Best Private Transport Malaysia</h1>
                        <p style="color: #FFFFFF;">
                            Need private chauffeur services? GoMyHire provides the Best Private Transport in
                            Malaysia. Book Chauffeur Services in KL for a smooth and comfy ride now!
                        </p>
                    </div>
                </div>
            </section>

            @include('landing.partials.contact.contact-form')
            @include('landing.partials.contact.address')
        </div>
        @include('landing.partials.landing.mobileapp')
        @include('landing.footer')
    </div>
@stop
@push('js')
<script>
    $(document).ready(function() {
        // Add flag icon classes to country code options
        $('#countryCode option').each(function() {
            const flagCode = $(this).data('flag');
            const flagClass = 'flag-icon-' + flagCode;
            $(this).addClass(flagClass);
        });
    });
</script>
@endpush
