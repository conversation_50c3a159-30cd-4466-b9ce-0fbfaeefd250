@php
    $popular_cars_sample = ['Economy Premium', 'Premium Tour Van', 'Luxury MPV', '10 Seater MPV / Van', 'Comfort 5 Seater', '14 seater van'];
    $large_cars_sample = ['Mid Size SUV', 'Standard Size MPV', 'Luxury MPV', 'Alphard / Velfire', '10 Seater MPV / Van', '44 Seater bus', '30 Seater mini bus'];
    $small_cars_sample = ['Compact 5 Seater', 'Comfort 5 Seater'];
@endphp
<style>
    .car_type_description {
        overflow: auto;
        height: 5rem;
        margin-bottom: 1rem;
    }
</style>
<section class="cars-section row">
    <p style="color: #9E2A9B; text-transform: uppercase; font-weight: 500;">COLLECTION</p>
    <h3>Pick Up to Airport & Chatered Car Types Rates Comparison</h3>
    <ul class="nav nav-tabs" id="collectionCar" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link active" aria-current="page" href="#" id="popular-tab" data-bs-toggle="tab"
                data-bs-target="#popular-tab-pane" role="tab" aria-controls="popular-tab-pane" aria-selected="true">
                <i class="fas fa-circle"></i>
                <span>Popular</span>
            </a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" aria-current="page" href="#" id="large-tab" data-bs-toggle="tab"
                data-bs-target="#large-tab-pane" role="tab" aria-controls="large-tab-pane" aria-selected="false"><i
                    class="fas fa-circle"></i> Large Car</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" aria-current="page" href="#" id="small-tab" data-bs-toggle="tab"
                data-bs-target="#small-tab-pane" role="tab" aria-controls="small-tab-pane" aria-selected="false"><i
                    class="fas fa-circle"></i> Small Car</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" aria-current="page" href="#" id="exclusive-tab" data-bs-toggle="tab"
                data-bs-target="#exclusive-tab-pane" role="tab" aria-controls="exclusive-tab-pane"
                aria-selected="false"><i class="fas fa-circle"></i> Exclusive Car</a>
        </li>
    </ul>
    <div class="tab-content py-3 px-0" id="collectionCarContent">
        <div class="tab-pane fade show active" id="popular-tab-pane" role="tabpanel" aria-labelledby="popular-tab"
            tabindex="0">
            <div class="row">
                @for ($i = 0; $i < count($popular_cars); $i++)
                    <div class="col-12 col-sm-4 my-3 px-0">
                        <div class="card">
                            <div class="car-img-container">
                                <div class="car-img">
                                    @if (
                                        $popular_cars[$i]->image_path != null &&
                                            file_exists(public_path('car_type_landing_image/' . $popular_cars[$i]->image_path)))
                                        <img src="{{ asset('car_type_landing_image/' . $popular_cars[$i]->image_path) }}"
                                            alt="" loading="lazy">
                                    @else
                                        <img src="{{ asset('images/landing/no_img.jpg') }}" alt="" loading="lazy">
                                    @endif
                                </div>
                            </div>
                            <div class="card-description">
                                {{-- <h4 class="m-0">From RM 200.00 
                                    <span style="color: #A3A3A3">/days</span>
                                </h4> --}}
                                <div class="car_type_description">
                                    <h4 class="m-0">{!! nl2br(e($popular_cars[$i]->from_price_per_day)) !!}</h4>
                                </div>
                                <p>{{ $popular_cars[$i]->type }}</p>
                            </div>
                            <div style="color: #8D8D8D" class="car-info">
                                <p class="mb-2">
                                    <img src="{{ asset('images/landing/user.svg') }}" alt="SVG Image"> <span
                                        style="margin-right: 1rem;">{{ $popular_cars[$i]->seat_number }} Pax</span>
                                    <img src="{{ asset('images/landing/luggage.svg') }}" alt="SVG Image">
                                    <span>{{ $popular_cars[$i]->luggage_number }} Luggages</span>
                                </p>
                            </div>
                            {{-- <p style="color: #8D8D8D">The car price might be changed depends on the high request.</p> --}}
                        </div>
                    </div>
                @endfor
            </div>
        </div>
        <div class="tab-pane fade" id="large-tab-pane" role="tabpanel" aria-labelledby="large-tab" tabindex="0">
            <div class="row">
                @for ($i = 0; $i < count($large_cars); $i++)
                    <div class="col-12 col-sm-4 my-3 px-0">
                        <div class="card">
                            <div class="car-img-container">
                                <div class="car-img">
                                    @if (
                                        $large_cars[$i]->image_path != null &&
                                            file_exists(public_path('car_type_landing_image/' . $large_cars[$i]->image_path)))
                                        <img src="{{ asset('car_type_landing_image/' . $large_cars[$i]->image_path) }}"
                                            alt="" loading="lazy">
                                    @else
                                        <img src="{{ asset('images/landing/no_img.jpg') }}" alt="" loading="lazy">
                                    @endif
                                </div>
                            </div>
                            <div class="card-description">
                                {{-- <h4 class="m-0">From RM 200.00 
                                    <span style="color: #A3A3A3">/days</span>
                                </h4> --}}
                                <div class="car_type_description">
                                    <h4 class="m-0">{!! nl2br(e($large_cars[$i]->from_price_per_day)) !!}</h4>
                                </div>
                                <p>{{ $large_cars[$i]->type }}</p>
                            </div>
                            <div style="color: #8D8D8D" class="car-info">
                                <p class="mb-2">
                                    <img src="{{ asset('images/landing/user.svg') }}" alt="SVG Image"> <span
                                        style="margin-right: 1rem;">{{ $large_cars[$i]->seat_number }} Pax</span>
                                    <img src="{{ asset('images/landing/luggage.svg') }}" alt="SVG Image">
                                    <span>{{ $large_cars[$i]->luggage_number }} Luggages</span>
                                </p>
                            </div>
                            {{-- <p style="color: #8D8D8D">The car price might be changed depends on the high request.</p> --}}
                        </div>
                    </div>
                @endfor
            </div>
        </div>
        <div class="tab-pane fade" id="small-tab-pane" role="tabpanel" aria-labelledby="small-tab" tabindex="0">
            <div class="row">
                @for ($i = 0; $i < count($small_cars); $i++)
                    <div class="col-12 col-sm-4 my-3 px-0">
                        <div class="card">
                            <div class="car-img-container">
                                <div class="car-img">
                                    @if (
                                        $small_cars[$i]->image_path != null &&
                                            file_exists(public_path('car_type_landing_image/' . $small_cars[$i]->image_path)))
                                        <img src="{{ asset('car_type_landing_image/' . $small_cars[$i]->image_path) }}"
                                            alt="" loading="lazy">
                                    @else
                                        <img src="{{ asset('images/landing/no_img.jpg') }}" alt=""
                                            loading="lazy">
                                    @endif
                                </div>
                            </div>
                            <div class="card-description">
                                {{-- <h4 class="m-0">From RM 200.00 
                                    <span style="color: #A3A3A3">/days</span>
                                </h4> --}}
                                <div class="car_type_description">
                                    <h4 class="m-0">{!! nl2br(e($small_cars[$i]->from_price_per_day)) !!}</h4>
                                </div>
                                <p>{{ $small_cars[$i]->type }}</p>
                            </div>
                            <div style="color: #8D8D8D" class="car-info">
                                <p class="mb-2">
                                    <img src="{{ asset('images/landing/user.svg') }}" alt="SVG Image"> <span
                                        style="margin-right: 1rem;">{{ $small_cars[$i]->seat_number }} Pax</span>
                                    <img src="{{ asset('images/landing/luggage.svg') }}" alt="SVG Image">
                                    <span>{{ $small_cars[$i]->luggage_number }} Luggages</span>
                                </p>
                            </div>
                            {{-- <p style="color: #8D8D8D">The car price might be changed depends on the high request.</p> --}}
                        </div>
                    </div>
                @endfor
            </div>
        </div>
        <div class="tab-pane fade" id="exclusive-tab-pane" role="tabpanel" aria-labelledby="exclusive-tab"
            tabindex="0">
            <div class="row">
                @for ($i = 0; $i < count($exclusive_cars); $i++)
                    <div class="col-12 col-sm-4 my-3 px-0">
                        <div class="card">
                            <div class="car-img-container">
                                <div class="car-img">
                                    @if (
                                        $exclusive_cars[$i]->image_path != null &&
                                            file_exists(public_path('car_type_landing_image/' . $exclusive_cars[$i]->image_path)))
                                        <img src="{{ asset('car_type_landing_image/' . $exclusive_cars[$i]->image_path) }}"
                                            alt="" loading="lazy">
                                    @else
                                        <img src="{{ asset('images/landing/no_img.jpg') }}" alt=""
                                            loading="lazy">
                                    @endif
                                </div>
                            </div>
                            <div class="card-description">
                                {{-- <h4 class="m-0">From RM 200.00 
                                    <span style="color: #A3A3A3">/days</span>
                                </h4> --}}
                                <div class="car_type_description">
                                    <h4 class="m-0">{!! nl2br(e($exclusive_cars[$i]->from_price_per_day)) !!}</h4>
                                </div>
                                <p>{{ $exclusive_cars[$i]->type }}</p>
                            </div>
                            <div style="color: #8D8D8D">
                                <p class="mb-2">
                                    <img src="{{ asset('images/landing/user.svg') }}" alt="SVG Image"
                                        style="margin-right: 1rem;"> {{ $exclusive_cars[$i]->seat_number }} Pax
                                    <img src="{{ asset('images/landing/luggage.svg') }}" alt="SVG Image">
                                    {{ $exclusive_cars[$i]->luggage_number }} Luggages
                                </p>
                            </div>
                            {{-- <p style="color: #8D8D8D">The car price might be changed depends on the high request.</p> --}}
                        </div>
                    </div>
                @endfor
            </div>
        </div>
    </div>
    <div class="pb-5" hidden>
        <a class="btn iphone-button" href="#"
            style="background: transparent; color: #333; border: 2px solid black; color: black;">
            View All Cars
        </a>
    </div>
</section>
