<section class="driver_question">
    <div class="question-content">
        @if (isset($driver_data['questions']))
            @foreach ($driver_data['questions'] as $q)
            <hr>
            <div class="question">
                <button class="toggle-btn">
                    <span style="color: #5f5f5f;">
                        <i class="fas fa-plus"></i>
                    </span>
                    &nbsp;&nbsp;{{ $q->question }}</button>
                <div class="answer">
                    <p>{{ $q->answer }}</p>
                </div>
            </div>
            @endforeach
        @else
            <hr>
            <div class="question">
                <button class="toggle-btn">
                    <span style="color: #5f5f5f;">
                        <i class="fas fa-plus"></i>
                    </span>
                    &nbsp;&nbsp;Does GoMyHire help with application of the PSV license?</button>
                <div class="answer">
                    <p>please let us know when your flight time had change, and notice us when actual boarding. We will rearrange another driver.</p>
                </div>
            </div>
            <hr>
            <div class="question">
                <button class="toggle-btn">
                    <span style="color: #5f5f5f;">
                        <i class="fas fa-plus"></i>
                    </span>
                    &nbsp;&nbsp;Does GoMyHire provide any reimbursement for the cost of getting my PSV license?</button>
                <div class="answer">
                    <p>please let us know when your flight time had change, and notice us when actual boarding. We will rearrange another driver.</p>
                </div>
            </div>
            <hr>
            <div class="question">
                <button class="toggle-btn">
                    <span style="color: #5f5f5f;">
                        <i class="fas fa-plus"></i>
                    </span>
                    &nbsp;&nbsp;What if I'm an existing GoMyHire Driver-Partner?</button>
                <div class="answer">
                    <p>please let us know when your flight time had change, and notice us when actual boarding. We will rearrange another driver.</p>
                </div>
            </div>
            <hr>
            <div class="question">
                <button class="toggle-btn">
                    <span style="color: #5f5f5f;">
                        <i class="fas fa-plus"></i>
                    </span>
                    &nbsp;&nbsp;What if I have concerns about safety?</button>
                <div class="answer">
                    <p>please let us know when your flight time had change, and notice us when actual boarding. We will rearrange another driver.</p>
                </div>
            </div>
            <hr>
            <div class="question">
                <button class="toggle-btn">
                    <span style="color: #5f5f5f;">
                        <i class="fas fa-plus"></i>
                    </span>
                    &nbsp;&nbsp;What is the status of my application?</button>
                <div class="answer">
                    <p>please let us know when your flight time had change, and notice us when actual boarding. We will rearrange another driver.</p>
                </div>
            </div>
        @endif
    </div>
</section>