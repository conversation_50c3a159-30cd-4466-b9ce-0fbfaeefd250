<section class="get_started">
    <h2>Get Started with No Upfront Costs</h2>
    <div class="home-buttons banner-buttons pb-4">
        <button type="button" class="iphone-button" style="background: #1A1A1A; color: #FFF;">
            Driver Partner
        </button>
        <button type="button" class="iphone-button" style="background: #FFFFFF; color: #767676; border: 1.5px solid #BABABA;">
            Travel Agencies
        </button>  
        <button type="button" class="iphone-button" style="background: #FFFFFF; color: #767676; border: 1.5px solid #BABABA;">
            Hotel Partner
        </button>                  
    </div>
    <div class="row py-3">
        <div class="col-sm-4">
            <div class="img_container">
                <img src="{{ asset('images/business/get_started_1.png') }}" alt="">
            </div>
        </div>
        <div class="col-sm-2">
            <div class="vertical-line"></div>
        </div>
        <div class="col-sm-6">
            <p style="font-weight:600;">Set your limits</p>
            <p>
                Set ride and meal limits based on day, time, location, and budget. You can also let your team charge to a single company account or their personal cards.
            </p>
        </div>
    </div>
    <div class="row py-3">
        <div class="col-sm-4">
            <div class="img_container">
                <img src="{{ asset('images/business/get_started_2.png') }}" alt="">
            </div>
        </div>
        <div class="col-sm-2">
            <div class="vertical-line"></div>
        </div>
        <div class="col-sm-6">
            <p style="font-weight:600;">Invite eligible employees</p>
            <p>
                Set ride and meal limits based on day, time, location, and budget. You can also let your team charge to a single company account or their personal cards.
            </p>
        </div>
    </div>
    <div class="row py-3">
        <div class="col-sm-4">
            <div class="img_container">
                <img src="{{ asset('images/business/get_started_3.png') }}" alt="">
            </div>
        </div>
        <div class="col-sm-2">
            <div class="vertical-line vertical_line_last"></div>
        </div>
        <div class="col-sm-6">
            <p style="font-weight:600;">Get moving</p>
            <p>
                Set ride and meal limits based on day, time, location, and budget. You can also let your team charge to a single company account or their personal cards.
            </p>
        </div>
    </div>
    <div class="row py-3">
        <div class="col-sm-4">
            <div class="img_container">
                <img src="{{ asset('images/business/get_started_4.png') }}" alt="">
            </div>
        </div>
        <div class="col-sm-2"></div>
        <div class="col-sm-6">
            <p style="font-weight:600;">Track expenses</p>
            <p>
                Set ride and meal limits based on day, time, location, and budget. You can also let your team charge to a single company account or their personal cards.
            </p>
        </div>
    </div>
</section>