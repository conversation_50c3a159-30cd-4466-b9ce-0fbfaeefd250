<footer>
    <div class="footer-container">
        <div class="row offices">
            <div class="col-md-4">
                <p style="font-weight: 600;">{{ $landing_data['offices']['name'] ?? 'Offices' }}</p>
                @if (isset($landing_data['offices']['footer_content_items']))
                    @foreach ($landing_data['offices']['footer_content_items'] as $off_item)
                        <p style="color: #D1D1D1; font-weight: 600;" class="mb-2 mt-4">{{ $off_item['title'] ?? '' }}</p>
                        <div class="description">
                            {!! $off_item['description'] ?? '' !!}
                        </div>
                    @endforeach
                @endif
            </div>
            <div class="col-md-8 row">
                @foreach ($landing_data['site_menus'] as $site_menu)
                    <div class="col-md-3">
                        <p style="font-weight: 600;">{{ $site_menu['name'] }}</p>
                        <ul class="site-menus">
                            @foreach ($site_menu['footer_content_items'] as $item)
                                <li>
                                    @if ($item['content_type'] == 3)
                                    <a href="{{ route('extended_pages.pdf_view', $item['id']) }}" target="_blank">{{ $item['title'] }}</a>
                                    @else
                                    <a href="{{ $item['extend_url'] }}" target="_blank">{{ $item['title'] }}</a>
                                    @endif
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="other-business">
            <div class="row">
                <p class="mt-4" style="font-weight: 600;">
                    {{ $landing_data['other_business']['name'] ?? 'Other Business' }}</p>
                @if (isset($landing_data['other_business']['footer_content_items']))
                    @foreach ($landing_data['other_business']['footer_content_items'] as $other_item)
                        <div class="col-md-4">
                            <p style="color: #D1D1D1; font-weight: 600;" class="mb-2">{{ $other_item['title'] ?? '' }}
                            </p>
                            <div class="description">
                                {!! $other_item['description'] ?? '' !!}
                            </div>
                        </div>
                    @endforeach
                @endif
                <div class="col-md-4">
                    <ul class="social-media-icons">
                        @if (isset($landing_data['social_links']['footer_content_items']))
                            @foreach ($landing_data['social_links']['footer_content_items'] as $link)
                                <li><a href="{{ $link['extend_url'] ?? '' }}"><i class="{{ $link['icon'] ?? '' }}"></i></a></li>
                            @endforeach
                        @endif
                        {{-- <li><a href="#"><i class="fab fa-facebook"></i></a></li>
                        <li><a href="#"><i class="fab fa-instagram"></i></a></li>
                        <li><a href="#"><i class="fab fa-youtube"></i></a></li> --}}
                        <li class="footer-brand">
                            <a href="/">
                                <img src="{{ asset('images/landing/img-logo-customer-magenta.png') }}" alt="GoMyHire"
                                    style="width: 100%;">
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div>
            <div class="row">
                <p class="mt-4 mb-0" style="font-weight: 600;">{{$year}}<br>
                    @if (isset($landing_data['copy_right']['footer_content_items'][0]))
                        {!! $landing_data['copy_right']['footer_content_items'][0]['title'] !!}
                    @else
                        <br>GoMyHire Travel Sdn Bhd
                        <br>Sky Mirror World Tour Sdn Bhd Original
                        <br>All Rights Reserved</p>
                    @endif
                </p>
            </div>
        </div>
        <div class="row mt-3">
            <p class="mt-3 mb-0">Our server is hosted on:
                <a href="https://www.ipserverone.com/data-center-malaysia/" target="_blank">https://www.ipserverone.com/data-center-malaysia</a>
            </p>
        </div>
    </div>
</footer>
