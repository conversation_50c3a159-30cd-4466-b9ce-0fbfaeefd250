<!DOCTYPE html>
<html>

<head>
    @include('landing.partials.google_analytics')
    <title> KLIA Airport Transfer Service | Charter Car Service Malaysia</title>
    <meta name="description" content="Need an airport transfer car service? Our KLIA Airport Transfer Service has you covered! Book a Charter Car Service to KLIA today. Best rates guaranteed.">
    <link rel="stylesheet" href="{{ asset('/css/bootstrap/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/bootstrap/jquery.datetimepicker.min.css') }}">
    {{-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"> --}}
    <link rel="stylesheet" href="{{ asset('/vendor/fontawesome-free/css/all.min.css') }}">
    {{-- <link rel="stylesheet" href="{{ asset('/css/home.min.css') }}"> --}}
    <link rel="stylesheet" href="{{ asset('/css/home.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/partials/landing/banner.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/partials/landing/prebooking.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/partials/landing/services.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/partials/landing/drive-success.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/partials/landing/advantages.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/partials/landing/tabs.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/partials/footer.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/partials/landing/media-queries.css') }}">
</head>

<body>
    @include('landing.navbar')

    <div class="content" style="background-color: #F8F7F6">
        <div style="position: relative;">
            <section class="banner"
                style="background-image: url({{ $landing_data['banner_content']['background_image_path'] ?? '' }})">
                <div class="banner-content">
                    <h1 class="text-capitalize">
                        {{ $landing_data['banner_content']['title'] ?? 'Book Your Airport Transfer & Chartered Car service Around Malaysia' }}
                    </h1>
                    <div style="max-width: 75%;">
                        <p style="color: #5F5F5F;">
                            {{ $landing_data['banner_content']['description'] ??
                                'Drive performance and your cross-functional collaboration with
                                                            easy-to-use dashboards,data visualization, and automated insights
                                                            in one click.' }}
                        </p>
                    </div>
                    <div class="home-buttons banner-buttons row">
                        <div class="banner_btns_container p-0 col-xxl-10 col-xl-9 col-lg-12 col-md-12 col-sm-12 d-flex align-items-start">
                            <a href="https://gomyhire.com.my/scan_qr_code_book?qrCode=240725wvw514" class="iphone-button"
                            style="background: #9E2A9B; color: #FFFFFF;" target="_blank">
                                Book Airport Transfer
                            </a>
                            <button class="iphone-button whatsapp"
                                style="background: transparent; color: #1A1A1A; border: 2px solid #1A1A1A;"
                                onclick="book_via_whatsapp()">
                                <img src="{{ asset('images/landing/Vector.svg') }}" alt=""> <span
                                    class="mt-2">Chat with us</span>
                            </button>
                            <button type="button" class="iphone-button" data-toggle="modal"
                            data-target="#searchCustomerBookingModal" style="background: transparent; color: #1A1A1A; border: 2px solid #1A1A1A;">
                            Search My Booking
                            </button>
                            {{-- <button type="button" class="iphone-button"
                            style="background: transparent; color: #1A1A1A; border: 2px solid #1A1A1A;" data-toggle="modal" data-target="#chatCodeModal">
                            <i class="fas fa-comment"></i> Chat with us
                            </button> --}}

                        </div>
                    </div>

                    {{-- Client Requested to remove QRs - 13-03-2025 --}}
                    {{-- <div class="d-flex align-items-start">
                        @php
                            $chat_url = "https://gomyhire.com.my/general_chat/qr_code/check_code";
                            $chat_qr = generateDynamicQRCode($chat_url, 150);
                            $airport_transfer_url = "https://gomyhire.com.my/scan_qr_code_book?qrCode=240725wvw514";
                            $airport_transfer_qr = generateDynamicQRCode($airport_transfer_url, 150);
                        @endphp
                        <div class="p-3">
                            <strong>Scan QR to chat</strong>
                            <div class="qr_container">
                                {!! $chat_qr !!}
                            </div>
                        </div>
                        <div class="p-3">
                            <strong>Scan QR to book Airport Transfer</strong>
                            <div class="qr_container">
                                {!! $airport_transfer_qr !!}
                            </div>
                        </div>

                    </div> --}}
                    {{-- <div class="home-buttons banner-buttons d-flex align-items-start" style="margin-top: 1rem;">
                        <button type="button" class="iphone-button"
                        style="background: transparent; color: #1A1A1A; border: 2px solid #1A1A1A;" data-toggle="modal" data-target="#chatCodeModal">
                        <i class="fas fa-comment"></i> Chat with us
                        </button>
                        <div style="padding-left: 1rem;">
                            <strong>Scan QR to chat</strong>
                            <div class="qr_container" style="width: 40%;">
                                <img src="{{ asset('images/landing/qr_code_general_chat.jpeg') }}" alt="" style="width: 100%;">
                            </div>
                        </div>
                    </div> --}}

                </div>
            </section>
            {{-- @include('landing.partials.landing.prebooking') --}}
            <section class="services-section d-flex justify-content-center">
                <div class="text-center">
                    <p style="color: #BBA86D; text-transform: uppercase; font-weight: 500;">Our Services</p>
                    <h1 style="text-transform: capitalize;">
                        {{ $landing_data['service_content']['title'] ?? 'Malaysia No.1 Leading Airport Transfer, Charter Car Chauffeur Service' }}
                    </h1>

                    <p style="color: #8D8D8D;">
                        {{ $landing_data['service_content']['description'] ??
                            'GoMyHire covers nearly every corner of Malaysia and airports that allow customers to book the service directly on its website confidently.' }}
                    </p>
                </div>
            </section>
            @include('landing.partials.landing.advantages')
            <div class="blank-container"></div>
        </div>
        @include('landing.partials.landing.collection-cars')
        @include('landing.partials.landing.booking')
        {{-- @include('landing.partials.landing.other-services') --}}
        {{-- @include('landing.partials.landing.drive-success') --}}
        @include('landing.partials.landing.partners')
        @include('landing.partials.landing.questions')
        @include('landing.partials.landing.mobileapp')
        @include('landing.footer')

    </div>
    @include('landing.partials.landing.search_customer_booking')
    <div class="modal fade" id="chatCodeModal" tabindex="-1" role="dialog" aria-labelledby="demoModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Do you already have a chat code?</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="submitChatCode">
                        @csrf
                        <div class="form-group row mx-0 col-md-12">
                            <label for="chat_code" class="col-md-4 col-form-label">Chat Code</label>
                            <input type="text" id="chat_code" name="chat_code" class="col-md-8">
                        </div>
                    </form>
                </div>
                <div class="modal-footer justify-content-between align-items-center">
                    <div>
                        <button type="button" class="btn btn-link" onclick="startNewChat()">Start New Chat</button>
                        {{-- <button type="button" onclick="showQrCode()" class="btn btn-link">Start New Chat</button> --}}
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" style="background-color:#9E2A9B; border: 1px solid #a431a0;"
                            onclick="submit_chat_code()">Go to Chat</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="qrCodeModal" tabindex="-1" role="dialog" aria-labelledby="demoModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header justify-content-end">
                    <button type="button" onclick="closeQrModal()">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="qrcodeContainer" class="text-center my-3" style="width: 50%;margin: 0 auto;"></div>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="{{ asset('/js/jquery-3.7.0.min.js') }}"></script>
<script src="{{ asset('/js/bootstrap/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('/js/bootstrap/bootstrap.min.js') }}"></script>
<script src="{{ asset('/js/bootstrap/jquery.datetimepicker.full.min.js') }}"></script>
<script src="https://maps.googleapis.com/maps/api/js?key={{ config('app.google_map_key') }}&libraries=places"></script>
<script src="{{ asset('js/custom/qrcode.min.js') }}"></script>
<script>
    $(document).ready(function() {
        $('.toggle-btn').on('click', function() {
            $(this).siblings('.answer').slideToggle();
        });
        $('#switch-place').on('click', function() {
            var value1 = $('#pick_up').val();
            var value2 = $('#drop_off').val();

            $('#pick_up').val(value2);
            $('#drop_off').val(value1);
        });
        $('#date_time').datetimepicker({
            format: 'D, d M - h:m a',
            step: 1
        });

    });

    function initAutocomplete(id) {
        var input = document.getElementById(id);
        var options = {
            types: ['geocode'],
            componentRestrictions: {
                country: 'MY'
            }
        };
        var autocomplete = new google.maps.places.Autocomplete(input, options);
    }

    function book_via_whatsapp() {
        var text =
            "Name: %0aMobile: %0aEmail: %0aNumber of Passengers: %0aPickup Location: %0aDestination Location: %0aPickup Date: %0aFlight Number: %0aExtra Services: %0a - Meet and Greet x 0%0a - Baby Chair x 0%0a - Tour Guide x 0%0a - Other Requirements: ";
        window.open(
            'https://api.whatsapp.com/send?phone=60162234711&text=' + text,
            '_blank'
        );
    }

    function submit_chat_code() {
        var chatCode = $('#chat_code').val();
        if(chatCode){
            let url = '/general_chat?chat_code=' + encodeURIComponent(chatCode);
            window.open(url, '_blank');
            $('#chatCodeModal').modal('hide');
        }
        else{
            alert('Chat code is required!');
        }
    }

    var qrCode = null;
    function showQrCode(){
        document.getElementById("qrcodeContainer").innerHTML = '';
        if (!qrCode) {
            qrCode = new QRCode(document.getElementById("qrcodeContainer"), {
                text: "{{ route('general_chat') }}",
                width: 250,
                height: 250
            });
        }
        $('#qrCodeModal').modal('show');
    }

    $('#qrCodeModal').on('hidden.bs.modal', function () {
        if (qrCode !== null) {
            qrCode.clear();
            qrCode = null;
            document.getElementById("qrcodeContainer").innerHTML = '';
        }
    });

    function closeQrModal(){
        if (qrCode !== null) {
            qrCode.clear();
            qrCode = null;
            document.getElementById("qrcodeContainer").innerHTML = '';
        }
        $('#qrCodeModal').modal('hide');
    }

    function startNewChat(){
        $.ajax({
            url: '/general_chat/get_new_chat_code',
            type: 'GET',
            success: function(response) {
                let newCode = response.new_code;
                let url = '/general_chat?chat_code=' + encodeURIComponent(newCode);
                window.open(url, '_blank');
            },
            error: function(xhr, status, error) {
                console.error(error);
            }
        });

    }
</script>

</html>
