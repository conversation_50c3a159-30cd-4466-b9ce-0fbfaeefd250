<section class="pre-booking">
    <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item px-3 prebooking-tab" role="presentation">
            <a class="nav-link active" aria-current="page" href="#" id="prebooking-tab"
                data-bs-toggle="tab" data-bs-target="#prebooking-tab-pane" role="tab"
                aria-controls="prebooking-tab-pane" aria-selected="true">Pre-booking</a>
        </li>
        <li class="nav-item charters px-3" role="presentation">
            <a class="nav-link disabled" aria-current="page" href="#" id="charters-tab" data-bs-toggle="tab"
                data-bs-target="#charters-tab-pane" role="tab" aria-controls="charters-tab-pane"
                aria-selected="false">Charters (Hourly)</a>
        </li>
    </ul>
    <div class="tab-content p-3" id="myTabContent" style="background: #fff;">
        <div class="tab-pane fade show active" id="prebooking-tab-pane" role="tabpanel" aria-labelledby="prebooking-tab"
            tabindex="0">
            <div class="d-flex location align-items-center">
                <div class="d-flex pick-up align-items-center">
                    <div class="location-img">
                        <img src="{{ asset('images/landing/location-filled.svg') }}" alt="">
                    </div>
                    <div style="width: 100%;">
                        <span>Pick up</span>
                        <input type="text" name="pick_up" placeholder="Enter a location" 
                        style="outline: none; border: none; width: 100%; color: #1A1A1A;" id="pick_up"
                        oninput="initAutocomplete('pick_up')">
                    </div>
                </div>
                <button class="switch-btn" type="button" id="switch-place">
                    <img src="{{ asset('images/landing/switch.svg') }}" alt="">
                </button>
                <div class="d-flex drop-off align-items-center">
                    <div class="location-img">
                        <img src="{{ asset('images/landing/location-filled.svg') }}" alt="">
                    </div>
                    <div style="width: 100%;">
                        <span>Drop off</span>
                        <input type="text" name="drop_off" placeholder="Enter a location" 
                        style="outline: none; border: none; width: 100%;" id="drop_off"
                        oninput="initAutocomplete('drop_off')">
                    </div>
                </div>
            </div>
            <div class="d-flex align-items-center mt-3">
                <div class="d-flex datetime align-items-center">
                    <div class="datetime-img">
                        <img src="{{ asset('images/landing/calendar.svg') }}" alt="">
                    </div>
                    <div style="width: 90%;">
                        <span>Pick up Time</span>
                        <input type="text" name="date_time" placeholder="Choose date&time" style="outline: none; border: none; width: 100%;"
                        id="date_time" disabled>
                    </div>
                </div>
                <div class="d-flex passengers align-items-center">
                    <div class="passengers-img">
                        <img src="{{ asset('images/landing/user-filled.svg') }}" alt="">
                    </div>
                    <div style="width: 100%;">
                        <span>Passengers</span>
                        <select class="form-select p-0" name="passenger" placeholder="Select"
                        style="outline: none; border: none; width: 100%;" disabled>
                            <option selected disabled>Select</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                        </select>                          
                    </div>
                    
                </div>
                <div class="d-flex luggage align-items-center">
                    <div class="luggage-img">
                        <img src="{{ asset('images/landing/luggage-filled.svg') }}" alt="">
                    </div>
                    <div style="width: 100%;">
                        <span>Luggage</span>
                        <select class="form-select p-0" name="luggage" placeholder="Select"
                        style="outline: none; border: none; width: 100%;" disabled>
                            <option selected disabled>Select</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                        </select> 
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-end switch-container">
                    <div class="form-check form-switch">
                        <label class="form-check-label" for="return_trip">Return trip?</label><br>
                        <input class="form-check-input m-0" type="checkbox" role="switch" id="return_trip" name="return_trip">
                    </div>
                    <button class="iphone-button"
                            style="background: #9E2A9B; border: 1px solid 9E2A9B; color: #fff;">Search
                    </button>
                </div>
            </div>   
        </div>
        {{-- <div class="row">
            <div class="col-12">
                <div class="d-flex location">
                    <div class="d-flex pick-up align-items-center">
                        <div class="location-img">
                            <img src="{{ asset('images/landing/location-filled.svg') }}" alt="">
                        </div>
                        <div style="width: 100%;">
                            <span>Pick up</span>
                            <input type="text" name="pick_up" placeholder="Enter a location" 
                            style="outline: none; border: none; width: 100%; color: #1A1A1A;" id="pick_up"
                            oninput="initAutocomplete('pick_up')">
                        </div>
                    </div>
                    <button class="switch-btn" type="button" id="switch-place">
                        <img src="{{ asset('images/landing/switch.svg') }}" alt="">
                    </button>
                    <div class="d-flex drop-off align-items-center">
                        <div class="location-img">
                            <img src="{{ asset('images/landing/location-filled.svg') }}" alt="">
                        </div>
                        <div style="width: 100%;">
                            <span>Drop off</span>
                            <input type="text" name="drop_off" placeholder="Enter a location" 
                            style="outline: none; border: none; width: 100%;" id="drop_off"
                            oninput="initAutocomplete('drop_off')">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-3 col-12">
                <div class="d-flex datetime" style="width: 100%;">
                    <div class="datetime-img">
                        <img src="{{ asset('images/landing/calendar.svg') }}" alt="">
                    </div>
                    <div style="width: 90%;">
                        <span>Pick up Time</span>
                        <input type="text" name="date_time" placeholder="Choose date&time" style="outline: none; border: none; width: 100%;"
                        id="date_time" disabled>
                    </div>
                </div>
            </div>
            <div class="col-sm-3 col-12">
                <div class="d-flex passengers" style="width: 100%;">
                    <div class="passengers-img">
                        <img src="{{ asset('images/landing/user-filled.svg') }}" alt="">
                    </div>
                    <div>
                        <span>Passengers</span>
                        <select class="form-select p-0" name="passenger" placeholder="Select"
                        style="outline: none; border: none; width: 100%;" disabled>
                            <option selected disabled>Select</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                        </select>                          
                    </div>
                    
                </div>
            </div>
            <div class="col-sm-3 col-12">
                <div class="d-flex luggage align-items-center" style="width: 100%;">
                    <div class="luggage-img">
                        <img src="{{ asset('images/landing/luggage-filled.svg') }}" alt="">
                    </div>
                    <div>
                        <span>Luggage</span>
                        <select class="form-select p-0" name="luggage" placeholder="Select"
                        style="outline: none; border: none; width: 100%;" disabled>
                            <option selected disabled>Select</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                        </select> 
                    </div>
                </div>
            </div>
            <div class="col-sm-3 col-12">
                <div class="d-flex align-items-center justify-content-end switch-container" style="width: 100%;">
                    <div class="form-check form-switch">
                        <label class="form-check-label" for="return_trip">Return trip?</label><br>
                        <input class="form-check-input m-0" type="checkbox" role="switch" id="return_trip" name="return_trip">
                    </div>
                    <button class="iphone-button"
                            style="background: #9E2A9B; border: 1px solid 9E2A9B; color: #fff;">Search
                    </button>
                </div>
            </div>
        </div> --}}
        <div class="tab-pane fade" id="charters-tab-pane" role="tabpanel" aria-labelledby="charters-tab"
            tabindex="0">
            Charters
        </div>
    </div>
</section>